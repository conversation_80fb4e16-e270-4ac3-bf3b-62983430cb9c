/* Real-time Chat Styles */

/* Online Status Indicators */
.online-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
    position: relative;
}

.online-indicator.online {
    background-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
    animation: pulse-online 2s infinite;
}

.online-indicator.offline {
    background-color: #6c757d;
}

@keyframes pulse-online {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Chat Messages */
.message-item {
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.own-message .message-bubble {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.other-message .message-bubble {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #e9ecef;
    margin-right: auto;
    border-bottom-left-radius: 5px;
}

.message-content {
    margin-bottom: 5px;
    line-height: 1.4;
}

.message-meta {
    font-size: 0.75rem;
    opacity: 0.8;
}

.message-status {
    margin-left: 5px;
    color: #28a745;
    font-weight: bold;
}

.message-status.read {
    color: #007bff;
}

/* Typing Indicator */
#typing-indicator {
    padding: 10px 15px;
    font-style: italic;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.typing-dots {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 10px;
}

.typing-dots div {
    position: absolute;
    top: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #007bff;
    animation: typing-dots 1.4s infinite ease-in-out both;
}

.typing-dots div:nth-child(1) { left: 0; animation-delay: -0.32s; }
.typing-dots div:nth-child(2) { left: 10px; animation-delay: -0.16s; }
.typing-dots div:nth-child(3) { left: 20px; animation-delay: 0; }

@keyframes typing-dots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Online Users List */
.online-users-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.online-users-header {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.online-count-badge {
    background: #28a745;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.75rem;
    font-weight: 500;
}

.online-user-item {
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.online-user-item:last-child {
    border-bottom: none;
}

.online-user-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 5px;
    padding-left: 5px;
}

/* Chat Container Improvements */
.chat-messages-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    scroll-behavior: smooth;
}

.chat-messages-container::-webkit-scrollbar {
    width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message Input */
.message-input-container {
    background: white;
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 5px;
    transition: border-color 0.3s ease;
}

.message-input-container:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.message-input {
    border: none;
    outline: none;
    padding: 10px 15px;
    border-radius: 20px;
    resize: none;
    max-height: 100px;
}

.send-button {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    border: none;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-button:hover {
    background: #0056b3;
    transform: scale(1.05);
}

.send-button:active {
    transform: scale(0.95);
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 5px;
    color: white;
    font-size: 0.875rem;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #28a745;
}

.connection-status.disconnected {
    background: #dc3545;
}

.connection-status.connecting {
    background: #ffc107;
    color: #212529;
}

/* Unread Message Indicator */
.unread-chat-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .message-bubble {
        max-width: 85%;
    }
    
    .online-users-container {
        padding: 10px;
    }
    
    .chat-messages-container {
        max-height: 300px;
        padding: 10px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .other-message .message-bubble {
        background: #343a40;
        color: #f8f9fa;
        border-color: #495057;
    }
    
    .online-users-container {
        background: #343a40;
        color: #f8f9fa;
    }
    
    .chat-messages-container {
        background: #212529;
        border-color: #495057;
    }
    
    .message-input-container {
        background: #343a40;
        border-color: #495057;
    }
    
    .message-input {
        background: transparent;
        color: #f8f9fa;
    }
}
